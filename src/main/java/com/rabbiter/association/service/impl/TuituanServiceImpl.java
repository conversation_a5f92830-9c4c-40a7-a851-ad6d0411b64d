package com.rabbiter.association.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.rabbiter.association.dao.TuituanDao;
import com.rabbiter.association.entity.Tuituan;
import com.rabbiter.association.service.TuituanService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Service
public class TuituanServiceImpl implements TuituanService {

    @Resource
    private TuituanDao tuituanDao;

    @Override
    public void add(Tuituan tuituan) {
        if (tuituan == null) {
            return;
        }
        // TODO: add tuituan to database
        if (tuituan.gettUid() == null) {
            return;
        }
        tuituan.settStarttime(LocalDateTime.now());
        tuituan.settStatus(0);
        tuituanDao.insert(tuituan);
    }

    @Override
    public void update(Tuituan tuituan) {
        if (tuituan == null || tuituan.gettUid() == null) {
            return;
        }

        tuituan.settEndtime(LocalDateTime.now());
        tuituan.settStatus(1);

        tuituanDao.updateById(tuituan);
    }

    @Override
    public void delete(Tuituan tuituan) {

    }

    @Override
    public Tuituan getOne(String id) {
        return null;
    }

    @Override
    public List<Tuituan> getListByManId(String manId) {
        QueryWrapper<Tuituan> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("t_uid", manId);
        return tuituanDao.selectList(queryWrapper);
    }

    @Override
    public List<Tuituan> getAll() {
        return Collections.emptyList();
    }
}
