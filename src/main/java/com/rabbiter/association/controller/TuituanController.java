package com.rabbiter.association.controller;


import com.rabbiter.association.entity.Tuituan;
import com.rabbiter.association.msg.R;
import com.rabbiter.association.service.TuituanService;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.List;

@Controller
@RequestMapping("/tuituan")
public class TuituanController {

    @Resource
    private TuituanService tuituanService;

    @PostMapping("/add")
    public R add(Tuituan tuituan) {
        tuituanService.add(tuituan);
        return R.success();
    }

    @PostMapping("/list")
    public R list(String manId) {
        System.out.println("manId:-----------" + manId);
        List<Tuituan> tuituans = tuituanService.getListByManId(manId);
        return R.successData(tuituans);
    }

    @PostMapping("/all")
    public R all() {
        List<Tuituan> tuituans = tuituanService.getAll();
        return R.successData(tuituans);
    }

    @PostMapping("/update")
    public R update(Tuituan tuituan) {
        tuituanService.update(tuituan);
        return R.success();
    }
}
