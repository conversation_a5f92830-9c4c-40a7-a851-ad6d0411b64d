package com.rabbiter.association.entity;



import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import java.time.LocalDateTime;


/**
* 
* @TableName tuituan
*/
@TableName(value = "tuituan")
public class Tuituan implements Serializable {

    /**
    * 
    */
    @TableId(value = "t_id")
    private Integer tId;
    /**
    * 退团用户
    */
    @TableField(value = "t_uid")
    private String tUid;
    /**
    * 退团名称
    */
    @TableField(value = "t_name")
    private String tName;
    /**
    * 退团原因
    */
    @TableField(value = "t_result")
    private String tResult;
    /**
    * 退团状态
    */
    @TableField(value = "t_status")
    private Integer tStatus;
    /**
    * 申请时间
    */
    @TableField(value = "t_starttime")
    private LocalDateTime tStarttime;
    /**
    * 退团时间
    */
    @TableField(value = "t_endtime")
    private LocalDateTime tEndtime;

    public Integer gettId() {
        return tId;
    }

    public void settId(Integer tId) {
        this.tId = tId;
    }

    public String gettUid() {
        return tUid;
    }

    public void settUid(String tUid) {
        this.tUid = tUid;
    }

    public String gettName() {
        return tName;
    }

    public void settName(String tName) {
        this.tName = tName;
    }

    public String gettResult() {
        return tResult;
    }

    public void settResult(String tResult) {
        this.tResult = tResult;
    }

    public Integer gettStatus() {
        return tStatus;
    }

    public void settStatus(Integer tStatus) {
        this.tStatus = tStatus;
    }

    public LocalDateTime gettStarttime() {
        return tStarttime;
    }

    public void settStarttime(LocalDateTime tStarttime) {
        this.tStarttime = tStarttime;
    }

    public LocalDateTime gettEndtime() {
        return tEndtime;
    }

    public void settEndtime(LocalDateTime tEndtime) {
        this.tEndtime = tEndtime;
    }
}
